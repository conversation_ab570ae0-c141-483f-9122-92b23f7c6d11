# Frontend-Backend Connection Setup

This guide will help you connect the React frontend to the FastAPI backend for the Incident Ticket Analysis platform.

## Prerequisites

- Python 3.8+ with pip
- Node.js 16+ with npm
- Azure OpenAI credentials (optional for testing)

## Backend Setup

1. **Install Python dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Set up environment variables (optional for full functionality):**
   ```bash
   # Create a .env file in the root directory
   AZURE_OPENAI_API_KEY=your_api_key_here
   AZURE_OPENAI_ENDPOINT=your_endpoint_here
   AZURE_OPENAI_DEPLOYMENT_NAME=your_deployment_name
   AZURE_OPENAI_API_VERSION=2024-12-01-preview
   ```

3. **Start the backend server:**
   ```bash
   uvicorn app:app --reload --port 8000
   ```

   The backend will be available at: http://localhost:8000
   API documentation: http://localhost:8000/docs

## Frontend Setup

1. **Navigate to the frontend directory:**
   ```bash
   cd frontend
   ```

2. **Install Node.js dependencies:**
   ```bash
   npm install
   ```

3. **Start the frontend development server:**
   ```bash
   npm run dev
   ```

   The frontend will be available at: http://localhost:8080

## Testing the Connection

1. **Run the connection test script:**
   ```bash
   python test_connection.py
   ```

2. **Manual testing:**
   - Open http://localhost:8080 in your browser
   - Try uploading an Excel file
   - Check browser console for any errors
   - Verify API calls in the Network tab

## API Endpoints

The frontend connects to these backend endpoints:

- `POST /upload` - Upload Excel files
- `POST /preprocess` - Preprocess uploaded data
- `POST /analyze` - Run AI analysis
- `GET /download/{session_id}/{file_type}` - Download reports

## Troubleshooting

### Backend Issues

1. **Port 8000 already in use:**
   ```bash
   uvicorn app:app --reload --port 8001
   # Update API_BASE_URL in frontend/src/services/api.ts
   ```

2. **Missing dependencies:**
   ```bash
   pip install fastapi uvicorn pandas openpyxl python-multipart
   ```

3. **CORS errors:**
   - Check that CORS middleware is properly configured in app.py
   - Verify frontend URL is in allowed origins

### Frontend Issues

1. **Port 8080 already in use:**
   ```bash
   npm run dev -- --port 8081
   ```

2. **API connection errors:**
   - Verify backend is running on port 8000
   - Check API_BASE_URL in frontend/src/services/api.ts
   - Look for CORS issues in browser console

3. **Build errors:**
   ```bash
   npm install
   npm run build
   ```

## Development Workflow

1. **Start both servers:**
   ```bash
   # Terminal 1 - Backend
   uvicorn app:app --reload --port 8000

   # Terminal 2 - Frontend
   cd frontend && npm run dev
   ```

2. **Test the full workflow:**
   - Upload a sample Excel file
   - Run preprocessing
   - Configure analysis settings
   - Run AI analysis
   - Download reports

## File Structure

```
├── app.py                 # FastAPI backend
├── frontend/              # React frontend
│   ├── src/
│   │   ├── services/api.ts    # API service layer
│   │   ├── hooks/useApi.ts    # React Query hooks
│   │   └── components/        # UI components
├── backend/               # Backend modules
├── data/                  # Data directories
└── requirements.txt       # Python dependencies
```

## Next Steps

- Configure Azure OpenAI credentials for full AI functionality
- Test with real incident data
- Deploy to production environment
- Set up monitoring and logging
