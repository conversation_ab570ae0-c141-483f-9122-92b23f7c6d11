import { Card, CardContent } from "@/components/ui/card";
import { LucideIcon } from "lucide-react";

interface MetricsCardProps {
  title: string;
  value: string | number;
  icon: LucideIcon;
  description?: string;
  trend?: {
    value: number;
    isPositive: boolean;
  };
}

const MetricsCard = ({ title, value, icon: Icon, description, trend }: MetricsCardProps) => {
  return (
    <Card className="shadow-card hover:shadow-elegant transition-all duration-300 bg-gradient-card">
      <CardContent className="p-6">
        <div className="flex items-center justify-between mb-2">
          <div className="bg-primary/10 p-2 rounded-lg">
            <Icon className="text-primary" size={20} />
          </div>
          {trend && (
            <span className={`text-sm font-medium ${
              trend.isPositive ? 'text-success' : 'text-destructive'
            }`}>
              {trend.isPositive ? '+' : ''}{trend.value}%
            </span>
          )}
        </div>
        
        <h3 className="font-semibold text-2xl mb-1">{value}</h3>
        <p className="text-sm text-muted-foreground font-medium">{title}</p>
        
        {description && (
          <p className="text-xs text-muted-foreground mt-2">{description}</p>
        )}
      </CardContent>
    </Card>
  );
};

export default MetricsCard;