import { Bar<PERSON>hart3, <PERSON><PERSON><PERSON><PERSON>, Zap } from "lucide-react";

const HeroSection = () => {
  return (
    <div className="relative overflow-hidden bg-gradient-hero text-white">
      <div className="absolute inset-0 bg-black/10" />
      
      <div className="relative max-w-7xl mx-auto px-6 py-12">
        {/* Logo and Title */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-6">
            <div className="bg-white/20 backdrop-blur-sm rounded-xl p-3 mr-4">
              <BarChart3 size={32} className="text-white" />
            </div>
            <h1 className="text-4xl font-bold">
              Incident Ticket Analysis Platform
            </h1>
          </div>
          
          <p className="text-xl text-white/90 max-w-2xl mx-auto">
            Transform your IT support data into strategic insights with AI-powered analysis
          </p>
        </div>

        {/* Feature highlights */}
        <div className="grid md:grid-cols-3 gap-6 mt-12">
          <div className="text-center">
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6">
              <FileText className="mx-auto mb-3 text-white" size={24} />
              <h3 className="font-semibold mb-2">Smart Processing</h3>
              <p className="text-sm text-white/80">
                Automated data preprocessing and validation
              </p>
            </div>
          </div>
          
          <div className="text-center">
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6">
              <Zap className="mx-auto mb-3 text-white" size={24} />
              <h3 className="font-semibold mb-2">AI Analysis</h3>
              <p className="text-sm text-white/80">
                Root cause analysis and pattern detection
              </p>
            </div>
          </div>
          
          <div className="text-center">
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6">
              <BarChart3 className="mx-auto mb-3 text-white" size={24} />
              <h3 className="font-semibold mb-2">Strategic Insights</h3>
              <p className="text-sm text-white/80">
                Executive reports and automation opportunities
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HeroSection;