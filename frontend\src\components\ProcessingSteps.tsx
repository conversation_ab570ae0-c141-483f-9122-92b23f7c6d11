import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import {
  Upload,
  Cog,
  Brain,
  FileText,
  CheckCircle,
  Clock,
  Play
} from "lucide-react";
import { usePreprocessData, useRunAnalysis, useProgressTracking } from "@/hooks/useApi";

interface ProcessingStepsProps {
  currentStep: number;
  isProcessing: boolean;
  processingProgress: number;
  onStartPreprocessing: () => void;
  onStartAnalysis: () => void;
  isPreprocessed: boolean;
  hasResults: boolean;
  selectedGroup: string | string[];
  filePath?: string;
  onPreprocessComplete?: (data: any) => void;
  onAnalysisComplete?: (data: any) => void;
}

const ProcessingSteps = ({
  currentStep,
  isProcessing,
  processingProgress,
  onStartPreprocessing,
  onStartAnalysis,
  isPreprocessed,
  hasResults,
  selectedGroup,
  filePath,
  onPreprocessComplete,
  onAnalysisComplete
}: ProcessingStepsProps) => {
  const preprocessMutation = usePreprocessData();
  const analysisMutation = useRunAnalysis();
  const { trackProgress } = useProgressTracking();
  const steps = [
    {
      id: 1,
      title: "Upload Data",
      description: "Upload Excel file with ticket data",
      icon: Upload,
      status: currentStep > 1 ? 'completed' : currentStep === 1 ? 'active' : 'pending'
    },
    {
      id: 2,
      title: "Data Preprocessing",
      description: "Clean and validate ticket data",
      icon: Cog,
      status: currentStep > 2 ? 'completed' : currentStep === 2 ? 'active' : 'pending'
    },
    {
      id: 3,
      title: "AI Analysis",
      description: "Generate insights and root cause analysis",
      icon: Brain,
      status: currentStep > 3 ? 'completed' : currentStep === 3 ? 'active' : 'pending'
    },
    {
      id: 4,
      title: "Results & Reports",
      description: "View analysis results and download reports",
      icon: FileText,
      status: currentStep === 4 ? 'completed' : 'pending'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-success';
      case 'active': return 'text-primary';
      default: return 'text-muted-foreground';
    }
  };

  const getStatusIcon = (status: string, StepIcon: any) => {
    if (status === 'completed') {
      return <CheckCircle className="text-success" size={20} />;
    } else if (status === 'active') {
      return <StepIcon className="text-primary" size={20} />;
    } else {
      return <Clock className="text-muted-foreground" size={20} />;
    }
  };

  const handleRealPreprocessing = async () => {
    if (!filePath) return;

    try {
      const result = await trackProgress(
        () => preprocessMutation.mutateAsync(filePath),
        (progress) => {
          // This will be handled by the parent component's progress state
        },
        4000 // 4 second progress simulation
      );

      onPreprocessComplete?.(result);
    } catch (error) {
      // Error handling is done by the mutation hook
    }
  };

  const handleRealAnalysis = async () => {
    if (!filePath) return;

    try {
      const result = await trackProgress(
        () => analysisMutation.mutateAsync({
          filePath,
          brandName: "Enterprise", // This should come from props or config
          assignmentGroup: selectedGroup === "All" ? undefined : selectedGroup
        }),
        (progress) => {
          // This will be handled by the parent component's progress state
        },
        8000 // 8 second progress simulation for analysis
      );

      onAnalysisComplete?.(result);
    } catch (error) {
      // Error handling is done by the mutation hook
    }
  };

  return (
    <div className="space-y-6">
      {/* Step 2: Preprocessing */}
      {currentStep >= 2 && !isPreprocessed && (
        <Card className="shadow-card">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Cog size={20} />
              Step 2: Data Preprocessing
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4">
              Clean and prepare your ticket data for AI analysis
            </p>
            
            {(isProcessing && currentStep === 2) || preprocessMutation.isPending ? (
              <div className="space-y-3">
                <Progress value={processingProgress} />
                <p className="text-sm text-center">
                  Processing data... {Math.round(processingProgress)}%
                </p>
              </div>
            ) : (
              <Button
                onClick={filePath ? handleRealPreprocessing : onStartPreprocessing}
                className="w-full"
                disabled={!filePath}
              >
                <Play size={16} className="mr-2" />
                Start Preprocessing
              </Button>
            )}
          </CardContent>
        </Card>
      )}

      {/* Step 3: AI Analysis - Only show if groups are selected */}
      {currentStep >= 3 && isPreprocessed && !hasResults && selectedGroup && (
        <Card className="shadow-card">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Brain size={20} />
              Step 3: AI-Powered Analysis
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Badge variant="secondary" className="mb-2">
                Ready for Analysis
              </Badge>
              
              <p className="text-muted-foreground">
                Run AI analysis to identify patterns, root causes, and automation opportunities
              </p>
              
              {/* Show selected groups info */}
              <div className="p-3 bg-muted/50 rounded-lg">
                <p className="text-sm">
                  <strong>Selected:</strong> {
                    selectedGroup === "All" 
                      ? "All Assignment Groups" 
                      : Array.isArray(selectedGroup) 
                      ? selectedGroup.join(", ") 
                      : selectedGroup
                  }
                </p>
              </div>
              
              {(isProcessing && currentStep === 3) || analysisMutation.isPending ? (
                <div className="space-y-3">
                  <Progress value={processingProgress} />
                  <p className="text-sm text-center">
                    AI analysis in progress... {Math.round(processingProgress)}%
                  </p>
                </div>
              ) : (
                <Button
                  onClick={filePath ? handleRealAnalysis : onStartAnalysis}
                  className="w-full"
                  disabled={!filePath}
                >
                  <Brain size={16} className="mr-2" />
                  Run AI Analysis
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Message when no groups selected */}
      {currentStep >= 3 && isPreprocessed && !hasResults && !selectedGroup && (
        <Card className="shadow-card border-dashed">
          <CardContent className="pt-6">
            <div className="text-center text-muted-foreground">
              <Brain size={48} className="mx-auto mb-4 opacity-50" />
              <h3 className="font-semibold mb-2">Select Assignment Groups</h3>
              <p className="text-sm">
                Please select assignment groups in the configuration panel to run AI analysis
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Progress Overview */}
      <Card className="shadow-card">
        <CardHeader>
          <CardTitle>Progress Overview</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {steps.map((step) => (
              <div key={step.id} className="flex items-center gap-3 p-3 rounded-lg border">
                {getStatusIcon(step.status, step.icon)}
                <div className="flex-1">
                  <h4 className={`font-semibold ${getStatusColor(step.status)}`}>
                    {step.title}
                  </h4>
                  <p className="text-sm text-muted-foreground">
                    {step.description}
                  </p>
                </div>
                <Badge variant={
                  step.status === 'completed' ? 'default' :
                  step.status === 'active' ? 'secondary' : 'outline'
                }>
                  {step.status === 'completed' ? 'Done' :
                   step.status === 'active' ? 'Active' : 'Pending'}
                </Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ProcessingSteps;