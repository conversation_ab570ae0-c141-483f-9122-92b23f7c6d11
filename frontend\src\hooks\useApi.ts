// React Query hooks for API operations
import { useMutation, useQuery } from '@tanstack/react-query';
import { api, ApiError, UploadResponse, PreprocessResponse, AnalysisResponse } from '@/services/api';
import { useToast } from '@/hooks/use-toast';

// Upload file mutation
export function useUploadFile() {
  const { toast } = useToast();

  return useMutation({
    mutationFn: (file: File) => api.uploadFile(file),
    onSuccess: (data: UploadResponse) => {
      toast({
        title: "File uploaded successfully",
        description: `${data.total_tickets} tickets loaded from ${data.total_assignment_groups} assignment groups`,
      });
    },
    onError: (error: ApiError) => {
      toast({
        title: "Upload failed",
        description: error.message,
        variant: "destructive",
      });
    },
  });
}

// Preprocess data mutation
export function usePreprocessData() {
  const { toast } = useToast();

  return useMutation({
    mutationFn: (filePath: string) => api.preprocessData(filePath),
    onSuccess: (data: PreprocessResponse) => {
      toast({
        title: "Data preprocessed successfully",
        description: `${data.row_count} tickets processed and ready for analysis`,
      });
    },
    onError: (error: ApiError) => {
      toast({
        title: "Preprocessing failed",
        description: error.message,
        variant: "destructive",
      });
    },
  });
}

// Run analysis mutation
export function useRunAnalysis() {
  const { toast } = useToast();

  return useMutation({
    mutationFn: ({ 
      filePath, 
      brandName, 
      assignmentGroup 
    }: { 
      filePath: string; 
      brandName: string; 
      assignmentGroup?: string | string[]; 
    }) => api.runAnalysis(filePath, brandName, assignmentGroup),
    onSuccess: (data: AnalysisResponse) => {
      toast({
        title: "Analysis completed successfully",
        description: "Your incident analysis report is ready for download",
      });
    },
    onError: (error: ApiError) => {
      toast({
        title: "Analysis failed",
        description: error.message,
        variant: "destructive",
      });
    },
  });
}

// Download report mutation
export function useDownloadReport() {
  const { toast } = useToast();

  return useMutation({
    mutationFn: ({ sessionId, fileType }: { sessionId: string; fileType: 'doc' | 'json' }) => 
      api.downloadReport(sessionId, fileType),
    onSuccess: (blob: Blob, variables) => {
      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `analysis_report.${variables.fileType === 'doc' ? 'docx' : 'json'}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast({
        title: "Download started",
        description: `Report downloaded successfully`,
      });
    },
    onError: (error: ApiError) => {
      toast({
        title: "Download failed",
        description: error.message,
        variant: "destructive",
      });
    },
  });
}

// Progress tracking utility
export function useProgressTracking() {
  const trackProgress = async (
    operation: () => Promise<any>,
    onProgress: (progress: number) => void,
    duration: number = 5000
  ) => {
    const startTime = Date.now();
    const interval = 100; // Update every 100ms
    
    const progressInterval = setInterval(() => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min((elapsed / duration) * 100, 95); // Cap at 95% until completion
      onProgress(progress);
    }, interval);

    try {
      const result = await operation();
      clearInterval(progressInterval);
      onProgress(100);
      return result;
    } catch (error) {
      clearInterval(progressInterval);
      throw error;
    }
  };

  return { trackProgress };
}
