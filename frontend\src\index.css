@tailwind base;
@tailwind components;
@tailwind utilities;

/* Professional Incident Analysis Platform Design System */

@layer base {
  :root {
    /* HCL Tech Brand Colors */
    --hcl-blue: 210 100% 50%;
    --hcl-blue-dark: 210 100% 35%;
    --hcl-blue-light: 210 100% 85%;
    
    /* Core Design System */
    --background: 0 0% 100%;
    --foreground: 210 20% 15%;

    --card: 0 0% 100%;
    --card-foreground: 210 20% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 210 20% 15%;

    /* Primary - HCL Blue theme */
    --primary: 210 100% 50%;
    --primary-foreground: 0 0% 100%;
    --primary-hover: 210 100% 45%;
    --primary-glow: 210 100% 85%;

    /* Secondary - Elegant grays */
    --secondary: 210 15% 95%;
    --secondary-foreground: 210 20% 15%;
    --secondary-hover: 210 15% 90%;

    /* Status colors */
    --success: 142 76% 36%;
    --success-foreground: 0 0% 100%;
    --warning: 38 92% 50%;
    --warning-foreground: 38 92% 15%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    /* Interface elements */
    --muted: 210 15% 96%;
    --muted-foreground: 210 10% 45%;
    --accent: 210 20% 92%;
    --accent-foreground: 210 20% 15%;
    --border: 210 20% 88%;
    --input: 210 20% 88%;
    --ring: 210 100% 50%;

    /* Gradients */
    --gradient-hero: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--hcl-blue-dark)));
    --gradient-card: linear-gradient(145deg, hsl(var(--card)), hsl(var(--accent)));
    --gradient-data: linear-gradient(90deg, hsl(var(--primary) / 0.1), hsl(var(--success) / 0.1));

    /* Shadows */
    --shadow-elegant: 0 10px 30px -10px hsl(var(--primary) / 0.2);
    --shadow-card: 0 2px 8px -2px hsl(var(--foreground) / 0.1);
    --shadow-glow: 0 0 40px hsl(var(--primary-glow) / 0.4);

    /* Animations */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);

    --radius: 0.75rem;
  }

  .dark {
    --background: 210 20% 8%;
    --foreground: 210 15% 92%;

    --card: 210 20% 10%;
    --card-foreground: 210 15% 92%;

    --popover: 210 20% 10%;
    --popover-foreground: 210 15% 92%;

    --primary: 210 100% 55%;
    --primary-foreground: 210 20% 8%;
    --primary-hover: 210 100% 60%;

    --secondary: 210 20% 15%;
    --secondary-foreground: 210 15% 92%;
    --secondary-hover: 210 20% 20%;

    --success: 142 76% 45%;
    --warning: 38 92% 60%;
    --destructive: 0 84% 65%;

    --muted: 210 20% 12%;
    --muted-foreground: 210 10% 60%;
    --accent: 210 20% 15%;
    --accent-foreground: 210 15% 92%;
    --border: 210 20% 20%;
    --input: 210 20% 20%;

    --shadow-elegant: 0 10px 30px -10px hsl(0 0% 0% / 0.5);
    --shadow-card: 0 2px 8px -2px hsl(0 0% 0% / 0.3);
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}
