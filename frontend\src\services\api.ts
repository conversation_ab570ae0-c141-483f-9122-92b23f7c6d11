// API service layer for backend communication
const API_BASE_URL = 'http://localhost:8000'; // FastAPI default port

// Types for API responses
export interface UploadResponse {
  message: string;
  total_tickets: number;
  total_columns: number;
  total_assignment_groups: number;
  sample_data: any[];
  file_path: string;
}

export interface PreprocessResponse {
  message: string;
  processed_file: string;
  row_count: number;
}

export interface AnalysisResponse {
  message: string;
  session_id: string;
  doc_file: string;
  json_file: string;
}

// Custom error class for API errors
export class ApiError extends Error {
  constructor(
    message: string,
    public status?: number,
    public details?: any
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

// Generic API request function with error handling
async function apiRequest<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  const url = `${API_BASE_URL}${endpoint}`;
  
  try {
    const response = await fetch(url, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new ApiError(
        errorData.detail || `HTTP ${response.status}: ${response.statusText}`,
        response.status,
        errorData
      );
    }

    return await response.json();
  } catch (error) {
    if (error instanceof ApiError) {
      throw error;
    }
    
    // Network or other errors
    throw new ApiError(
      error instanceof Error ? error.message : 'Network error occurred',
      0,
      error
    );
  }
}

// File upload with FormData
async function uploadFile(file: File): Promise<UploadResponse> {
  const formData = new FormData();
  formData.append('file', file);

  const response = await fetch(`${API_BASE_URL}/upload`, {
    method: 'POST',
    body: formData,
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new ApiError(
      errorData.detail || `Upload failed: ${response.statusText}`,
      response.status,
      errorData
    );
  }

  return await response.json();
}

// Preprocess data
async function preprocessData(filePath: string): Promise<PreprocessResponse> {
  return apiRequest<PreprocessResponse>('/preprocess', {
    method: 'POST',
    body: JSON.stringify({ file_path: filePath }),
  });
}

// Run analysis
async function runAnalysis(
  filePath: string,
  brandName: string,
  assignmentGroup?: string | string[]
): Promise<AnalysisResponse> {
  const body: any = {
    file_path: filePath,
    brand_name: brandName,
  };

  if (assignmentGroup && assignmentGroup !== 'All') {
    body.assignment_group = assignmentGroup;
  }

  return apiRequest<AnalysisResponse>('/analyze', {
    method: 'POST',
    body: JSON.stringify(body),
  });
}

// Download report
async function downloadReport(sessionId: string, fileType: 'doc' | 'json'): Promise<Blob> {
  const response = await fetch(`${API_BASE_URL}/download/${sessionId}/${fileType}`);
  
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new ApiError(
      errorData.detail || `Download failed: ${response.statusText}`,
      response.status,
      errorData
    );
  }

  return await response.blob();
}

// Export all API functions
export const api = {
  uploadFile,
  preprocessData,
  runAnalysis,
  downloadReport,
};

export default api;
