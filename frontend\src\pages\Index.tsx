import { useState } from "react";
import { Sidebar, <PERSON>bar<PERSON>ontent, SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar";
import HeroSection from "@/components/HeroSection";
import ConfigurationPanel from "@/components/ConfigurationPanel";
import FileUploadSection from "@/components/FileUploadSection";
import ProcessingSteps from "@/components/ProcessingSteps";
import AnalysisResults from "@/components/AnalysisResults";
import MetricsCard from "@/components/MetricsCard";
import { FileSpreadsheet, BarChart3, Users, Clock } from "lucide-react";

const Index = () => {
  // Application state
  const [currentStep, setCurrentStep] = useState(1);
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingProgress, setProcessingProgress] = useState(0);
  const [uploadedData, setUploadedData] = useState<any>(null);
  const [isPreprocessed, setIsPreprocessed] = useState(false);
  const [hasResults, setHasResults] = useState(false);
  const [analysisResults, setAnalysisResults] = useState<any>(null);
  const [filePath, setFilePath] = useState<string>("");
  const [sessionId, setSessionId] = useState<string>("");

  // Configuration state
  const [selectedGroup, setSelectedGroup] = useState<string | string[]>("All");
  const [availableGroups, setAvailableGroups] = useState<string[]>([]);
  const [groupCounts, setGroupCounts] = useState<Record<string, number>>({});

  const handleFileUpload = (data: any) => {
    setUploadedData(data);
    setFilePath(data.file_path);
    setCurrentStep(2);

    // Extract assignment groups from sample data if available
    if (data.sample_data && Array.isArray(data.sample_data)) {
      const groups = [...new Set(data.sample_data.map((item: any) => item.Assignment_Group).filter(Boolean))];
      setAvailableGroups(groups);

      // Create mock counts for now - in a real app, this would come from the backend
      const counts: Record<string, number> = {};
      groups.forEach(group => {
        counts[group] = Math.floor(Math.random() * 200) + 50; // Mock count
      });
      setGroupCounts(counts);
    }
  };

  const simulateProgress = async (onComplete: () => void) => {
    setIsProcessing(true);
    setProcessingProgress(0);
    
    for (let i = 0; i <= 100; i += 5) {
      await new Promise(resolve => setTimeout(resolve, 100));
      setProcessingProgress(i);
    }
    
    setIsProcessing(false);
    setProcessingProgress(0);
    onComplete();
  };

  const handleStartPreprocessing = () => {
    simulateProgress(() => {
      setIsPreprocessed(true);
      setCurrentStep(3);
    });
  };

  const handlePreprocessComplete = (data: any) => {
    setIsPreprocessed(true);
    setCurrentStep(3);
  };

  const handleStartAnalysis = () => {
    simulateProgress(() => {
      setAnalysisResults({
        // Mock analysis results
        brand: "Enterprise",
        scope: Array.isArray(selectedGroup) ? selectedGroup.join(", ") : selectedGroup,
        timestamp: new Date().toISOString(),
        selectedGroups: selectedGroup
      });
      setHasResults(true);
      setCurrentStep(4);
    });
  };

  const handleAnalysisComplete = (data: any) => {
    setAnalysisResults({
      brand: "Enterprise",
      scope: Array.isArray(selectedGroup) ? selectedGroup.join(", ") : selectedGroup,
      timestamp: new Date().toISOString(),
      selectedGroups: selectedGroup,
      sessionId: data.session_id,
      docFile: data.doc_file,
      jsonFile: data.json_file
    });
    setSessionId(data.session_id);
    setHasResults(true);
    setCurrentStep(4);
  };

  const handleGroupSelect = (group: string | string[]) => {
    setSelectedGroup(group);
    setHasResults(false);
    setAnalysisResults(null);
    if (isPreprocessed) setCurrentStep(3);
  };

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-background">
        {/* Sidebar */}
        <Sidebar className="border-r">
          <SidebarContent className="p-6">
            <ConfigurationPanel
              selectedGroup={selectedGroup}
              onGroupSelect={handleGroupSelect}
              availableGroups={availableGroups}
              groupCounts={groupCounts}
              isPreprocessed={isPreprocessed}
            />
          </SidebarContent>
        </Sidebar>

        {/* Main Content */}
        <div className="flex-1 flex flex-col">
          {/* Hero Section */}
          <HeroSection />

          {/* Main Content Area */}
          <div className="flex-1 p-6 max-w-7xl mx-auto w-full">
            <div className="grid lg:grid-cols-3 gap-6">
              {/* Left Column - Upload & Processing */}
              <div className="lg:col-span-1 space-y-6">
                <SidebarTrigger className="lg:hidden mb-4" />
                
                <FileUploadSection
                  onFileUpload={handleFileUpload}
                  isProcessing={isProcessing && currentStep === 1}
                  isPreprocessed={isPreprocessed}
                />

                <ProcessingSteps
                  currentStep={currentStep}
                  isProcessing={isProcessing}
                  processingProgress={processingProgress}
                  onStartPreprocessing={handleStartPreprocessing}
                  onStartAnalysis={handleStartAnalysis}
                  isPreprocessed={isPreprocessed}
                  hasResults={hasResults}
                  selectedGroup={selectedGroup}
                  filePath={filePath}
                  onPreprocessComplete={handlePreprocessComplete}
                  onAnalysisComplete={handleAnalysisComplete}
                />
              </div>

              {/* Right Column - Results */}
              <div className="lg:col-span-2">
                {uploadedData && !hasResults && (
                  <div className="space-y-6">
                    <h2 className="text-2xl font-bold">Data Overview</h2>
                    
                    {/* Overview Metrics */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <MetricsCard
                        title="Total Tickets"
                        value={uploadedData.totalTickets?.toLocaleString() || "0"}
                        icon={FileSpreadsheet}
                      />
                      <MetricsCard
                        title="Assignment Groups"
                        value={uploadedData.assignmentGroups || "0"}
                        icon={Users}
                      />
                      <MetricsCard
                        title="Data Columns"
                        value={uploadedData.columns || "0"}
                        icon={BarChart3}
                      />
                    </div>

                    {/* Data Preview */}
                    {isPreprocessed && (
                      <div className="bg-gradient-data p-6 rounded-lg border">
                        <h3 className="font-semibold mb-3">✅ Data Successfully Preprocessed</h3>
                        <p className="text-muted-foreground">
                          Your ticket data has been cleaned and prepared for AI analysis. 
                          Select an assignment group and run the analysis to generate insights.
                        </p>
                      </div>
                    )}
                  </div>
                )}

                {hasResults && analysisResults && (
                  <AnalysisResults
                    analysisData={analysisResults}
                    brandName="Enterprise"
                    selectedGroup={selectedGroup}
                    sessionId={sessionId}
                  />
                )}

                {!uploadedData && (
                  <div className="flex items-center justify-center h-96 border-2 border-dashed border-border rounded-lg">
                    <div className="text-center">
                      <FileSpreadsheet className="mx-auto text-muted-foreground mb-4" size={48} />
                      <h3 className="font-semibold mb-2">No Data Uploaded</h3>
                      <p className="text-muted-foreground">
                        Upload your ticket data to begin the analysis process
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Footer */}
          <footer className="border-t py-6 px-6 text-center text-sm text-muted-foreground">
            Incident Ticket Analysis Platform - Transform your IT support data into strategic insights
          </footer>
        </div>
      </div>
    </SidebarProvider>
  );
};

export default Index;
