import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardT<PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { CheckCircle, XCircle, Settings } from "lucide-react";

interface ConfigurationPanelProps {
  selectedGroup: string | string[];
  onGroupSelect: (group: string | string[]) => void;
  availableGroups: string[];
  groupCounts: Record<string, number>;
  isPreprocessed: boolean;
}

const ConfigurationPanel = ({
  selectedGroup,
  onGroupSelect,
  availableGroups,
  groupCounts,
  isPreprocessed
}: ConfigurationPanelProps) => {
  const [azureConfigured] = useState(true); // Mock configuration

  return (
    <Card className="shadow-card">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Settings size={20} />
          Configuration
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Azure OpenAI Status */}
        <div>
          <h4 className="font-semibold mb-2">Azure OpenAI</h4>
          <div className="flex items-center gap-2">
            {azureConfigured ? (
              <>
                <CheckCircle className="text-success" size={16} />
                <span className="text-success text-sm">Connected</span>
              </>
            ) : (
              <>
                <XCircle className="text-destructive" size={16} />
                <span className="text-destructive text-sm">Not configured</span>
              </>
            )}
          </div>
        </div>

        {/* Brand Configuration */}
        <div>
          <h4 className="font-semibold mb-2">Brand Settings</h4>
          <Badge variant="secondary">Enterprise</Badge>
        </div>

        {/* Assignment Group Selection */}
        {isPreprocessed && availableGroups.length > 0 && (
          <div>
            <h4 className="font-semibold mb-3">Assignment Groups</h4>
            
            <div className="space-y-3 max-h-64 overflow-y-auto">
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="all-groups"
                  checked={selectedGroup === "All"}
                  onChange={() => onGroupSelect("All")}
                  className="rounded border-border"
                />
                <label htmlFor="all-groups" className="text-sm font-medium">
                  All Groups ({Object.values(groupCounts).reduce((a, b) => a + b, 0)} tickets)
                </label>
              </div>
              
              {availableGroups.map((group) => (
                <div key={group} className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id={`group-${group}`}
                    checked={Array.isArray(selectedGroup) ? selectedGroup.includes(group) : selectedGroup === group}
                    onChange={() => {
                      if (selectedGroup === "All") {
                        onGroupSelect([group]);
                      } else if (Array.isArray(selectedGroup)) {
                        const newSelection = selectedGroup.includes(group)
                          ? selectedGroup.filter(g => g !== group)
                          : [...selectedGroup, group];
                        onGroupSelect(newSelection.length > 0 ? newSelection : "All");
                      } else {
                        onGroupSelect(selectedGroup === group ? "All" : [selectedGroup, group]);
                      }
                    }}
                    className="rounded border-border"
                  />
                  <label htmlFor={`group-${group}`} className="text-sm">
                    {group} ({groupCounts[group] || 0} tickets)
                  </label>
                </div>
              ))}
            </div>
            
            {selectedGroup && (
              <div className="mt-3 p-3 bg-muted rounded-lg">
                <p className="text-sm text-muted-foreground">
                  {selectedGroup === "All" 
                    ? `Selected: All groups (${Object.values(groupCounts).reduce((a, b) => a + b, 0)} tickets)`
                    : Array.isArray(selectedGroup)
                    ? `Selected: ${selectedGroup.length} groups (${selectedGroup.reduce((sum, group) => sum + (groupCounts[group] || 0), 0)} tickets)`
                    : `Selected: ${groupCounts[selectedGroup] || 0} tickets`
                  }
                </p>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ConfigurationPanel;