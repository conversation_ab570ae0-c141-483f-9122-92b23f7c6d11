#!/usr/bin/env python3
"""
Simple script to test if the backend is running and accessible.
"""

import requests
import sys

def test_backend_connection():
    """Test if the FastAPI backend is running."""
    backend_url = "http://localhost:8000"
    
    try:
        # Test basic connectivity
        response = requests.get(f"{backend_url}/docs", timeout=5)
        if response.status_code == 200:
            print("✅ Backend is running and accessible!")
            print(f"   FastAPI docs available at: {backend_url}/docs")
            return True
        else:
            print(f"❌ Backend responded with status code: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to backend. Make sure it's running on port 8000.")
        print("   To start the backend, run: uvicorn app:app --reload --port 8000")
        return False
        
    except requests.exceptions.Timeout:
        print("❌ Backend connection timed out.")
        return False
        
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_cors():
    """Test CORS configuration."""
    backend_url = "http://localhost:8000"
    
    try:
        # Simulate a preflight request
        headers = {
            'Origin': 'http://localhost:8080',
            'Access-Control-Request-Method': 'POST',
            'Access-Control-Request-Headers': 'Content-Type'
        }
        
        response = requests.options(f"{backend_url}/upload", headers=headers, timeout=5)
        
        if 'Access-Control-Allow-Origin' in response.headers:
            print("✅ CORS is properly configured!")
            return True
        else:
            print("⚠️  CORS might not be properly configured.")
            return False
            
    except Exception as e:
        print(f"⚠️  Could not test CORS: {e}")
        return False

if __name__ == "__main__":
    print("Testing backend connection...")
    print("=" * 50)
    
    backend_ok = test_backend_connection()
    cors_ok = test_cors()
    
    print("\n" + "=" * 50)
    if backend_ok and cors_ok:
        print("🎉 All tests passed! Frontend should be able to connect to backend.")
        sys.exit(0)
    else:
        print("❌ Some tests failed. Check the issues above.")
        sys.exit(1)
