import { useState, useCallback } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Upload, Download, FileSpreadsheet, CheckCircle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useUploadFile, useProgressTracking } from "@/hooks/useApi";
import { UploadResponse } from "@/services/api";

interface FileUploadSectionProps {
  onFileUpload: (data: UploadResponse & { fileName: string }) => void;
  isProcessing: boolean;
  isPreprocessed: boolean;
}

const FileUploadSection = ({ onFileUpload, isProcessing, isPreprocessed }: FileUploadSectionProps) => {
  const [uploadProgress, setUploadProgress] = useState(0);
  const [dragActive, setDragActive] = useState(false);
  const { toast } = useToast();
  const uploadMutation = useUploadFile();
  const { trackProgress } = useProgressTracking();

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    const files = Array.from(e.dataTransfer.files);
    const excelFile = files.find(file => 
      file.name.endsWith('.xlsx') || file.name.endsWith('.xls')
    );
    
    if (excelFile) {
      handleFileUpload(excelFile);
    } else {
      toast({
        title: "Invalid file type",
        description: "Please upload an Excel file (.xlsx or .xls)",
        variant: "destructive",
      });
    }
  }, [toast]);

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileUpload(file);
    }
  };

  const handleFileUpload = async (file: File) => {
    try {
      setUploadProgress(0);

      const result = await trackProgress(
        () => uploadMutation.mutateAsync(file),
        setUploadProgress,
        3000 // 3 second progress simulation
      );

      // Transform API response to match expected format
      const uploadData = {
        ...result,
        fileName: file.name,
        totalTickets: result.total_tickets,
        assignmentGroups: result.total_assignment_groups,
        columns: result.total_columns,
        rawData: result.sample_data
      };

      onFileUpload(uploadData);
    } catch (error) {
      setUploadProgress(0);
      // Error handling is done by the mutation hook
    }
  };

  const downloadTemplate = () => {
    toast({
      title: "Template downloaded",
      description: "Sample template has been downloaded",
    });
  };

  return (
    <div className="space-y-6">
      {/* Template Download */}
      <Card className="shadow-card">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Download size={20} />
            Template Download
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground mb-4">
            Download the Excel template to see the required data format
          </p>
          <Button onClick={downloadTemplate} variant="outline" className="w-full">
            <Download size={16} className="mr-2" />
            Download Template
          </Button>
        </CardContent>
      </Card>

      {/* File Upload */}
      <Card className="shadow-card">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload size={20} />
            Upload Ticket Data
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div
            className={`
              border-2 border-dashed rounded-lg p-8 text-center transition-colors
              ${dragActive 
                ? 'border-primary bg-primary/5' 
                : 'border-border hover:border-primary/50'
              }
              ${isPreprocessed ? 'bg-success/5 border-success' : ''}
            `}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
          >
            {isPreprocessed ? (
              <div className="space-y-2">
                <CheckCircle className="mx-auto text-success" size={48} />
                <p className="text-success font-semibold">File uploaded and processed!</p>
                <p className="text-sm text-muted-foreground">Ready for analysis</p>
              </div>
            ) : (
              <div className="space-y-4">
                <FileSpreadsheet className="mx-auto text-muted-foreground" size={48} />
                <div>
                  <p className="font-semibold mb-2">Drop your Excel file here</p>
                  <p className="text-sm text-muted-foreground mb-4">
                    or click to browse (.xlsx, .xls files)
                  </p>
                  <input
                    type="file"
                    accept=".xlsx,.xls"
                    onChange={handleFileInput}
                    className="hidden"
                    id="file-upload"
                  />
                  <Button asChild variant="outline">
                    <label htmlFor="file-upload" className="cursor-pointer">
                      Choose File
                    </label>
                  </Button>
                </div>
              </div>
            )}
          </div>

          {(isProcessing || uploadMutation.isPending) && (
            <div className="mt-4 space-y-2">
              <Progress value={uploadProgress} />
              <p className="text-sm text-center text-muted-foreground">
                {uploadMutation.isPending ? 'Uploading...' : 'Processing...'} {Math.round(uploadProgress)}%
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default FileUploadSection;