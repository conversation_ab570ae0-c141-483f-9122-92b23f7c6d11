
"""
OpenAI utilities for Incident Ticket Analysis Project
Functions for setting up OpenAI clients, calling the API, and estimating token usage
"""

import os
import time
import tiktoken
import pandas as pd
from openai import AzureOpenAI
import logging

logger = logging.getLogger(__name__)

def setup_openai() -> AzureOpenAI:
    """Initialize Azure OpenAI client using environment variables."""
    api_key = os.getenv("AZURE_OPENAI_API_KEY")
    endpoint = os.getenv("AZURE_OPENAI_ENDPOINT")
    deployment = os.getenv("AZURE_OPENAI_DEPLOYMENT_NAME")
    api_version = os.getenv("AZURE_OPENAI_API_VERSION", '2024-12-01-preview')

    if not all([api_key, endpoint, deployment]):
        raise ValueError("Missing required Azure OpenAI environment variables")

    client = AzureOpenAI(
        api_key=api_key,
        api_version=api_version,
        azure_endpoint=endpoint,
    )
    return client


def estimate_tokens(text: str, model: str = "gpt-4") -> int:
    try:
        encoding = tiktoken.encoding_for_model(model)
    except KeyError:
        encoding = tiktoken.get_encoding("cl100k_base")
    return len(encoding.encode(text))



def call_openai_with_retry(client: AzureOpenAI, prompt: str, retries: int = 3) -> str:
    """Call Azure OpenAI API with retry logic."""
    deployment_name = os.getenv("AZURE_OPENAI_DEPLOYMENT_NAME")
    
    for attempt in range(retries):
        try:
            response = client.chat.completions.create(
                model=deployment_name,
                messages=[
                    {"role": "system", "content": "You are a senior IT analyst generating structured business insights. Always use exact field names from the provided data."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3
            )
            return response.choices[0].message.content.strip()
        
        except Exception as e:
            if "429" in str(e):
                logger.warning(f"Rate limit hit. Waiting 60 seconds... (Attempt {attempt + 1})")
                time.sleep(60)
            else:
                logger.error(f"OpenAI call failed on attempt {attempt + 1}: {e}")
                if attempt < retries - 1:
                    time.sleep(10)
                else:
                    raise RuntimeError("OpenAI call failed after retries.")