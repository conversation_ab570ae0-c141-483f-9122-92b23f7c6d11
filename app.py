from fastapi import <PERSON><PERSON><PERSON>, UploadFile, File, HTTPException
from fastapi.responses import FileResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Optional
import pandas as pd
import os
import json
from backend.preprocessing import preprocess_df
from backend.openai_utils import setup_openai,estimate_tokens, call_openai_with_retry
from backend.prompt_builder import build_analysis_prompt
from backend.batch_processing import process_tickets_in_batches
from backend.data_utils import load_and_validate_data,prepare_ticket_summary,estimate_tokens_per_ticket
from backend.result_utils import safe_parse_json
from backend.reporting import (create_word_document, write_json_report)
from config import Config

app = FastAPI()

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:8080", "http://127.0.0.1:8080"],  # Frontend dev server
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# -------------------
# Directories
# -------------------
RAW_DIR = Config.RAW_DIR
CLEANED_DIR = Config.CLEANED_DIR
RESULTS_DIR = Config.RESULTS_DIR

os.makedirs(RAW_DIR, exist_ok=True)
os.makedirs(CLEANED_DIR, exist_ok=True)
os.makedirs(RESULTS_DIR, exist_ok=True)

# -------------------
# Request Models
# -------------------
class PreprocessRequest(BaseModel):
    file_path: str

class AnalysisRequest(BaseModel):
    file_path: str
    brand_name: str
    assignment_group: Optional[str] = None

# -------------------
# 1️⃣ Upload Endpoint
# -------------------
@app.post("/upload")
async def upload_file(file: UploadFile = File(...)):
    """Upload a raw Excel ticket file."""
    file_path = os.path.join(RAW_DIR, file.filename)
    
    # Save uploaded file
    with open(file_path, "wb") as f:
        f.write(await file.read())
    
    df = pd.read_excel(file_path)
    
    return {
        "message": "File uploaded successfully",
        "total_tickets": len(df),
        "total_columns": len(df.columns),
        "total_assignment_groups": df["Assignment_Group"].nunique(),
        "sample_data": df.head(5).to_dict(orient="records"),
        "file_path": file_path
    }

# -------------------
# 2️⃣ Preprocess Endpoint
# -------------------
@app.post("/preprocess")
async def preprocess(request: PreprocessRequest):
    """Preprocess uploaded file and save to cleaned folder."""
    df = preprocess_df(request.file_path)
    
    cleaned_file_path = os.path.join(CLEANED_DIR, os.path.basename(request.file_path))
    df.to_excel(cleaned_file_path, index=False)
    
    return {
        "message": "Data successfully preprocessed",
        "processed_file": cleaned_file_path,
        "row_count": len(df)
    }

# -------------------
# 3️⃣ Analyze Endpoint
# -------------------
@app.post("/analyze")
async def analyze(request: AnalysisRequest):
    """Run full analysis pipeline and save results to results folder."""
    client = setup_openai()
    df = load_and_validate_data(request.file_path)

    # Filter by assignment group if provided
    if request.assignment_group and request.assignment_group.lower() != "all":
        if isinstance(request.assignment_group, (list, tuple, set)):
            df = df[df["Assignment_Group"].isin(request.assignment_group)]
        else:
            df = df[df["Assignment_Group"] == request.assignment_group]
        if df.empty:
            raise HTTPException(status_code=404, detail=f"No tickets found for group(s): {request.assignment_group}")

    # Prepare prompt for OpenAI
    ticket_summary = prepare_ticket_summary(df)
    full_tickets = df.to_dict(orient="records")
    prompt = build_analysis_prompt(ticket_summary, full_tickets)

    # Token count & batching
    token_count = estimate_tokens(prompt)
    if token_count > Config.MAX_PROMPT_TOKENS:
        avg_tokens = estimate_tokens_per_ticket(df)
        batch_size = max(5, Config.MAX_PROMPT_TOKENS // avg_tokens)
        result = process_tickets_in_batches(df, client, batch_size)
    else:
        raw_response = call_openai_with_retry(client, prompt, Config.MAX_RETRIES)
        result = safe_parse_json(raw_response)
        if not result:
            raise HTTPException(status_code=500, detail="Failed to parse analysis result")

    # Save outputs
    session_id = os.path.splitext(os.path.basename(request.file_path))[0]
    doc_file = os.path.join(RESULTS_DIR, f"{session_id}_analysis.docx")
    json_file = os.path.join(RESULTS_DIR, f"{session_id}_analysis.json")

    create_word_document(result, doc_file, request.brand_name, request.assignment_group)
    with open(json_file, "w") as f:
        json.dump(result, f, indent=2)
    
    write_json_report(result, json_file)

    return {
        "message": "Analysis completed successfully",
        "session_id": session_id,
        "doc_file": doc_file,
        "json_file": json_file
    }

# -------------------
# 4️⃣ Download Endpoint
# -------------------
@app.get("/download/{session_id}/{file_type}")
async def download_report(session_id: str, file_type: str):
    """Download reports by type: doc (Word) or json."""
    file_map = {
        "doc": os.path.join(RESULTS_DIR, f"{session_id}_analysis.docx"),
        "json": os.path.join(RESULTS_DIR, f"{session_id}_analysis.json"),
    }
    path = file_map.get(file_type)

    if not path or not os.path.exists(path):
        raise HTTPException(status_code=404, detail="File not found")

    return FileResponse(path, filename=os.path.basename(path))
