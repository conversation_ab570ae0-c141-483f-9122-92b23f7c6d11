import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  BarChart3,
  TrendingUp,
  AlertTriangle,
  Zap,
  Bot,
  Download,
  Target,
  Clock,
  Users
} from "lucide-react";
import MetricsCard from "./MetricsCard";

interface AnalysisResultsProps {
  analysisData: any;
  brandName: string;
  selectedGroup: string | string[];
}

const AnalysisResults = ({ analysisData, brandName, selectedGroup }: AnalysisResultsProps) => {
  const mockResults = {
    executiveSummary: {
      keyFindings: [
        "62% of incidents are related to infrastructure issues",
        "Average resolution time can be improved by 40% with automation",
        "3 critical patterns identified affecting multiple business units"
      ],
      strategicPriorities: [
        { priority: "1", timeline: "Immediate", description: "Implement automated monitoring for Oracle SOA" },
        { priority: "2", timeline: "3 months", description: "Deploy self-healing mechanisms for database connectivity" },
        { priority: "3", timeline: "6 months", description: "GenAI chatbot for L1 support automation" }
      ]
    },
    rootCauses: [
      {
        rootCause: "Database Connection Timeouts",
        ticketCount: 234,
        percentageOfTotal: 18.8,
        businessImpact: "Service degradation during peak hours",
        affectedApplications: ["Customer Portal", "Order Management"],
        automationFeasibility: "High - Can be automated with connection pooling"
      },
      {
        rootCause: "Oracle SOA Integration Failures", 
        ticketCount: 187,
        percentageOfTotal: 15.0,
        businessImpact: "Workflow disruptions affecting order processing",
        affectedApplications: ["OIC", "ERP Integration"],
        automationFeasibility: "Medium - Requires custom monitoring scripts"
      },
      {
        rootCause: "Email Service Downtime",
        ticketCount: 156,
        percentageOfTotal: 12.5,
        businessImpact: "Communication delays and missed notifications",
        affectedApplications: ["Notification Service", "Alert System"],
        automationFeasibility: "High - Failover mechanisms available"
      }
    ],
    automationOpportunities: [
      {
        ticketPattern: "Database connection failures",
        affectedTickets: 234,
        automationType: "Self-Healing",
        implementationEffort: "Medium",
        successCriteria: "90% auto-resolution rate"
      },
      {
        ticketPattern: "User access issues",
        affectedTickets: 189,
        automationType: "Automated Provisioning", 
        implementationEffort: "Low",
        successCriteria: "Instant access restoration"
      }
    ],
    genAiSolutions: [
      {
        useCase: "Intelligent Ticket Routing",
        description: "AI-powered classification and routing system",
        technologyStack: "Azure OpenAI + Logic Apps",
        implementationComplexity: "Medium",
        roiTimeline: "6 months"
      },
      {
        useCase: "Predictive Issue Detection", 
        description: "Proactive monitoring with ML anomaly detection",
        technologyStack: "Azure ML + Application Insights",
        implementationComplexity: "High",
        roiTimeline: "12 months"
      }
    ]
  };

  const displayScope = Array.isArray(selectedGroup) 
    ? selectedGroup.join(", ") 
    : selectedGroup === "All" 
    ? "All Assignment Groups" 
    : selectedGroup;

  const downloadReport = (type: 'word' | 'json') => {
    // Simulate download
    const scope = Array.isArray(selectedGroup) ? selectedGroup.join("_") : selectedGroup;
    const fileName = `${brandName}_${scope}_analysis_${new Date().toISOString().split('T')[0]}.${type === 'word' ? 'docx' : 'json'}`;
    console.log(`Downloading ${fileName}`);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Analysis Complete</h2>
          <p className="text-muted-foreground">
            Brand: {brandName} | Scope: {displayScope}
          </p>
        </div>
        <div className="flex gap-2">
          <Button onClick={() => downloadReport('word')} variant="outline">
            <Download size={16} className="mr-2" />
            Word Report
          </Button>
          <Button onClick={() => downloadReport('json')} variant="outline">
            <Download size={16} className="mr-2" />
            JSON Data
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <MetricsCard
          title="Total Incidents"
          value="1,247"
          icon={BarChart3}
          trend={{ value: 12, isPositive: false }}
        />
        <MetricsCard
          title="Avg Resolution Time"
          value="4.2h"
          icon={Clock}
          trend={{ value: 18, isPositive: true }}
        />
        <MetricsCard
          title="Automation Potential"
          value="67%"
          icon={Zap}
          trend={{ value: 25, isPositive: true }}
        />
        <MetricsCard
          title="Affected Teams"
          value="8"
          icon={Users}
        />
      </div>

      {/* Analysis Tabs */}
      <Tabs defaultValue="executive" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="executive">Executive Summary</TabsTrigger>
          <TabsTrigger value="observations">Observations</TabsTrigger>
          <TabsTrigger value="rootcause">Root Causes</TabsTrigger>
          <TabsTrigger value="automation">Automation</TabsTrigger>
          <TabsTrigger value="genai">GenAI Solutions</TabsTrigger>
        </TabsList>

        <TabsContent value="executive" className="space-y-6">
          <Card className="shadow-card">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target size={20} />
                Key Findings
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {mockResults.executiveSummary.keyFindings.map((finding, index) => (
                  <div key={index} className="flex items-start gap-3 p-3 bg-muted/50 rounded-lg">
                    <div className="bg-primary text-primary-foreground w-6 h-6 rounded-full flex items-center justify-center text-sm font-bold flex-shrink-0 mt-0.5">
                      {index + 1}
                    </div>
                    <p>{finding}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card className="shadow-card">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp size={20} />
                Strategic Priorities
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockResults.executiveSummary.strategicPriorities.map((priority, index) => (
                  <div key={index} className="border rounded-lg p-4">
                    <div className="flex items-start gap-3">
                      <Badge variant="secondary">{priority.priority}</Badge>
                      <Badge variant="outline">{priority.timeline}</Badge>
                    </div>
                    <p className="mt-2 font-medium">{priority.description}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="observations">
          <Card className="shadow-card">
            <CardHeader>
              <CardTitle>Key Observations & Insights</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="p-4 border rounded-lg">
                  <h4 className="font-semibold mb-2">Volume Patterns</h4>
                  <p>Peak incident volumes occur during business hours (9 AM - 5 PM) with 73% higher frequency</p>
                </div>
                <div className="p-4 border rounded-lg">
                  <h4 className="font-semibold mb-2">Team Performance</h4>
                  <p>Database team shows fastest resolution times (2.1h avg) while Oracle SOA requires optimization</p>
                </div>
                <div className="p-4 border rounded-lg">
                  <h4 className="font-semibold mb-2">Application Impact</h4>
                  <p>Customer-facing applications account for 45% of high-priority incidents</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="rootcause" className="space-y-4">
          {mockResults.rootCauses.map((cause, index) => (
            <Card key={index} className="shadow-card">
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span className="flex items-center gap-2">
                    <AlertTriangle size={20} />
                    {cause.rootCause}
                  </span>
                  <Badge variant="secondary">{cause.percentageOfTotal}%</Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-2 gap-4 mb-4">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Affected Tickets</p>
                    <p className="text-2xl font-bold">{cause.ticketCount}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Business Impact</p>
                    <p>{cause.businessImpact}</p>
                  </div>
                </div>
                
                <div className="space-y-3">
                  <div>
                    <p className="text-sm font-medium mb-1">Affected Applications:</p>
                    <div className="flex gap-2 flex-wrap">
                      {cause.affectedApplications.map((app, i) => (
                        <Badge key={i} variant="outline">{app}</Badge>
                      ))}
                    </div>
                  </div>
                  
                  <div className="p-3 bg-success/10 rounded-lg">
                    <p className="text-sm font-medium text-success mb-1">Automation Feasibility:</p>
                    <p className="text-sm">{cause.automationFeasibility}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        <TabsContent value="automation" className="space-y-4">
          {mockResults.automationOpportunities.map((opp, index) => (
            <Card key={index} className="shadow-card">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap size={20} />
                  {opp.ticketPattern}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-3 gap-4">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Affected Tickets</p>
                    <p className="text-2xl font-bold text-primary">{opp.affectedTickets}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Type</p>
                    <Badge>{opp.automationType}</Badge>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Effort</p>
                    <Badge variant="outline">{opp.implementationEffort}</Badge>
                  </div>
                </div>
                
                <div className="mt-4 p-3 bg-muted/50 rounded-lg">
                  <p className="text-sm font-medium mb-1">Success Criteria:</p>
                  <p className="text-sm">{opp.successCriteria}</p>
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        <TabsContent value="genai" className="space-y-4">
          {mockResults.genAiSolutions.map((solution, index) => (
            <Card key={index} className="shadow-card">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Bot size={20} />
                  {solution.useCase}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-2 gap-4 mb-4">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Complexity</p>
                    <Badge variant={
                      solution.implementationComplexity === 'High' ? 'destructive' :
                      solution.implementationComplexity === 'Medium' ? 'secondary' : 'default'
                    }>
                      {solution.implementationComplexity}
                    </Badge>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">ROI Timeline</p>
                    <p className="font-semibold">{solution.roiTimeline}</p>
                  </div>
                </div>
                
                <p className="mb-3">{solution.description}</p>
                
                <div className="p-3 bg-primary/5 border border-primary/20 rounded-lg">
                  <p className="text-sm font-medium mb-1">Technology Stack:</p>
                  <p className="text-sm">{solution.technologyStack}</p>
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AnalysisResults;